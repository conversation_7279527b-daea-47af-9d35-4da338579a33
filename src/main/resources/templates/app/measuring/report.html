<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <style>
        /* 报告头部背景样式 */
        .report-header-bg {
            position: relative;
            min-height: 280px;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            background-attachment: local;
            margin: -16px -16px 25px -16px;
            display: flex;
            align-items: flex-end;
            justify-content: center;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
        }

        .bg-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                180deg,
                rgba(0, 0, 0, 0.05) 0%,
                rgba(0, 0, 0, 0.02) 30%,
                rgba(0, 0, 0, 0.1) 70%,
                rgba(0, 0, 0, 0.3) 100%
            );
        }

        .header-content {
            position: relative;
            z-index: 3;
            width: 100%;
        }

        .report-header-bg .report-title-section {
            text-align: center;
            padding: 15px 20px 25px 20px;
            background: linear-gradient(
                135deg,
                rgba(255, 255, 255, 0.15) 0%,
                rgba(255, 255, 255, 0.05) 100%
            );
            backdrop-filter: blur(15px) saturate(1.2);
            border-top: 1px solid rgba(255, 255, 255, 0.3);
            margin: 100px 0 0 0;
            position: relative;
        }

        .report-header-bg .report-subtitle {
            text-shadow:
                0 2px 4px rgba(0, 0, 0, 0.9),
                0 1px 2px rgba(0, 0, 0, 0.7),
                0 0 15px rgba(255, 255, 255, 0.4);
            color: #ffffff !important;
            font-weight: 500;
            font-size: 16px;
            margin-bottom: 8px;
            opacity: 0.9;
        }

        .report-header-bg .report-main-title {
            text-shadow:
                0 3px 6px rgba(0, 0, 0, 0.9),
                0 1px 3px rgba(0, 0, 0, 0.7),
                0 0 20px rgba(255, 255, 255, 0.4);
            color: #ffffff !important;
            font-weight: 700;
            letter-spacing: 2px;
            font-size: 28px;
            text-transform: uppercase;
            position: relative;
            margin-bottom: 0;
        }

        .report-header-bg .report-main-title::after {
            content: '';
            position: absolute;
            bottom: -12px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, transparent, #ffffff, transparent);
            border-radius: 2px;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .report-header-bg {
                min-height: 240px;
                margin: -16px -16px 20px -16px;
            }

            .report-header-bg .report-title-section {
                padding: 12px 16px 20px 16px;
                margin: 80px 0 0 0;
            }

            .report-header-bg .report-main-title {
                font-size: 24px;
                letter-spacing: 1.5px;
            }

            .report-header-bg .report-subtitle {
                font-size: 15px;
            }
        }

        @media (max-width: 480px) {
            .report-header-bg {
                min-height: 220px;
                margin: -16px -16px 16px -16px;
            }

            .report-header-bg .report-title-section {
                padding: 10px 12px 18px 12px;
                margin: 60px 0 0 0;
            }

            .report-header-bg .report-main-title {
                font-size: 20px;
                letter-spacing: 1px;
            }

            .report-header-bg .report-subtitle {
                font-size: 14px;
            }

            .report-header-bg .report-main-title::after {
                width: 40px;
                height: 2px;
                bottom: -5px;
            }
        }

        /* 表格优化 */
        .table-responsive {
            -webkit-overflow-scrolling: touch;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .table {
            margin-bottom: 0;
            background: white;
        }

        .table thead th {
            position: sticky;
            top: 0;
            background: #f8f9fa;
            color: #495057;
            font-weight: 600;
            border-bottom: 2px solid #dee2e6;
            z-index: 10;
        }

        /* 图表容器样式优化 */
        .charts-container {
            overflow: hidden;
            width: 100%;
            max-width: 100%;
        }

        .chart-item {
            margin-bottom: 20px;
            overflow: visible;
            background: white;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            padding: 10px;
            width: 100%;
            max-width: 100%;
            text-align: center;
        }

        .chart-item > div {
            overflow: visible !important;
            width: 100% !important;
            max-width: 100% !important;
        }

        .chart-item canvas,
        .chart-item > div {
            max-width: 100% !important;
            width: 100% !important;
            height: auto !important;
            overflow: visible;
            margin: 0 auto;
        }

        /* 图表容器通用样式 */
        [id^="chartContainer"] {
            width: 100% !important;
            max-width: 100% !important;
            height: auto !important;
            max-height: 350px !important;
            margin: 0 auto;
            display: block;
        }

        /* 因子层级样式 */
        .factor-level {
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
            background: white;
        }

        .factor-level-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 16px;
            font-weight: 600;
            font-size: 16px;
        }

        .factor-level-content {
            padding: 16px;
            background: #f8f9fa;
        }

        .factor-section {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }

        .factor-main-title {
            font-size: 16px;
            font-weight: 700;
            color: #333;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #eee;
        }

        .factor-sub-title {
            color: #495057;
            font-weight: 600;
            margin-bottom: 10px;
            padding-left: 10px;
            border-left: 3px solid #727cf5;
        }

        .factor-charts-section {
            margin-top: 16px;
            padding: 12px;
            background: white;
            border-radius: 6px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .factor-charts-section h5 {
            font-size: 14px;
            margin-bottom: 10px;
            color: #495057;
        }

        /* 滚动提示 */
        .scroll-hint {
            text-align: center;
            font-size: 12px;
            color: #666;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 6px;
            margin-bottom: 8px;
            border: 1px solid #e9ecef;
        }

        /* 表格滚动指示器 */
        .table-responsive.scrollable {
            position: relative;
        }

        .table-responsive.scrollable::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            width: 20px;
            background: linear-gradient(to left, rgba(255,255,255,0.8), transparent);
            pointer-events: none;
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            #container_1, #container_2, #complex-container {
                min-height: 250px;
                width: 100% !important;
                max-width: 100% !important;
                overflow: visible !important;
            }

            .chart-item canvas {
                max-width: 100% !important;
                width: 100% !important;
            }

            [id^="chartContainer"] {
                height: auto !important;
                max-height: 350px !important;
                width: 100% !important;
                max-width: 100% !important;
            }
        }

        @media (max-width: 480px) {
            #container_1, #container_2, #complex-container {
                min-height: 200px;
                width: 100% !important;
                max-width: 100% !important;
                overflow: visible !important;
            }

            .chart-item {
                padding: 5px;
                margin: 5px 0;
            }

            .chart-item canvas {
                max-width: 100% !important;
                width: 100% !important;
                height: auto !important;
            }

            [id^="chartContainer"] {
                height: auto !important;
                max-height: 350px !important;
                width: 100% !important;
                max-width: 100% !important;
            }
        }
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="pageTitle">
            《<span class="scaleName"></span>》测试报告
        </div>
        <div class="right">
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
    </div>
    <!-- * App Header -->
    <div id="appCapsule">
        <div class="alert hide text-center text-success" id="alert">
            <div class="splashBlock">
                <div class="mb-3 mt-3">
                    <img th:src="@{/static/images/app/success.png}" alt="draw" width="100">
                </div>
                <div class="sectionTitle text-center">
                    <div class="lead font16 font-weight-bold" id="msg">

                    </div>
                </div>
            </div>
        </div>
        <div class="appContent" id="report">
            <!-- 报告头部背景区域 -->
            <div class="report-header-bg" id="reportHeaderBg" style="display: none;">
                <div class="bg-overlay"></div>
                <div class="header-content">
                    <div class="report-title-section">
                        <div>
                            <p class="report-subtitle text-white">《<span class="scaleName"></span>》</p>
                            <h1 class="report-main-title text-white">心理测试报告</h1>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 普通报告头部 -->
            <div class="report-header" id="normalHeader">
                <div class="report-title-section">
                    <div>
                        <p class="report-subtitle">《<span class="scaleName"></span>》</p>
                        <h1 class="report-main-title">心理测试报告</h1>
                    </div>
                </div>
            </div>

            <!-- 基本信息卡片 -->
            <div class="report-section">
                <div class="section-header">
                    <div class="section-icon">
                        <i class="fa fa-info-circle"></i>
                    </div>
                    <h2 class="section-title">基本信息</h2>
                </div>
                <div class="info-grid pt-0">
                    <div class="info-item pt-0 pb-0">
                        <span class="info-label">用户名</span>
                        <span class="info-value" id="overviewName"></span>
                    </div>
                    <div class="info-item pt-0 pb-0">
                        <span class="info-label">所属组织</span>
                        <span class="info-value" id="overviewOrg"></span>
                    </div>
                    <div class="info-item pt-0 pb-0">
                        <span class="info-label">测试耗时</span>
                        <span class="info-value" id="overviewTime"></span>
                    </div>
                    <div class="info-item pt-0 pb-0">
                        <span class="info-label">测试日期</span>
                        <span class="info-value" id="overviewDate"></span>
                    </div>
                </div>
            </div>

            <!-- 因子结果分析区域 -->
            <div id="factorAnalysisSection"></div>

            <!-- 额外的底部间距，确保内容不被遮挡 -->
            <div class="report-footer-spacer"></div>
        </div>
    </div>

    <th:block th:insert="~{layouts/footMeasuring}"/>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/hcharts/highcharts.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/highcharts-more.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/solid-gauge.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/exporting.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/export-data.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/rgbcolor.min.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/stackblur.min.js}"></script>
    <script th:src="@{/static/js/plugins/daterangepicker/moment.min.js}"></script>
    <script th:src="@{/static/js/plugins/canvg/canvg.js}"></script>
    <script th:src="@{/static/js/pages/chartDataConfig.js}"></script>
    <script type="text/javascript">
        let recordId = getUrlParam('recordId');
        let reportData, testRecord, scale, user;
        let chartsImgArray = []; // 图表图片数组

        let initReport = function () {
            layer.open({type: 2, content: '报告加载中…', shadeClose: false});
            $.ajax({
                type: "POST",
                url: "/measuringroom/testing/report?recordId=" + recordId,
                dataType: "json",
                contentType: "application/json",
                async: false,
                success: function (data) {
                    layer.closeAll();
                    let res = JSON.parse(data);
                    if (res.resultCode !== undefined) {
                        if (res.resultCode === 201) {
                            $("#report").hide();
                            $(".appHeader").hide();
                            $("#msg").append("您已经完成测试！");
                            $("#alert").removeClass('hide').addClass('show');
                            return;
                        } else if (res.resultCode !== 200) {
                            layer.msg(res.resultMsg || '获取报告失败', { icon: 2, time: 2000 });
                            return;
                        }
                    }
                    
                    if (!res.data || !res.data.testRecord) {
                        layer.msg('报告数据格式错误', { icon: 2, time: 2000 });
                        return;
                    }
                    
                    reportData = res.data;
                    testRecord = res.data.testRecord;
                    scale = res.data.testRecord.scale;
                    user = res.data.testRecord.user;

                    // 数据加载完成后初始化页面
                    getBaseInfo();
                    processReport();
                    if (getUrlParam("savecharts") === 'true') {
                        saveCharts();
                    }
                },
                error: function(xhr, status, error) {
                    layer.closeAll();
                    layer.msg('网络错误，请稍后重试', { icon: 2, time: 2000 });
                }
            });
        };
        
        let getBaseInfo = function () {
            $(".scaleName").html(scale.scaleName);
            $("#realName").html(user.realName === "" ? user.loginName : user.realName);
            $("#fullStructName").html(user.structFullName);
            $("#startDate").html(moment(testRecord.startTime).format("YYYY-MM-DD"));
            $("#costTime").html(formatSeconds(testRecord.timeInterval));

            // 填充新UI的数据
            $("#statusDate").html(moment(testRecord.startTime).format("YYYY-MM-DD"));
            $("#heroName").html(user.realName === "" ? user.loginName : user.realName);
            $("#heroCostTime").html(formatSeconds(testRecord.timeInterval));
            $("#overviewName").html(user.realName === "" ? user.loginName : user.realName);
            $("#overviewOrg").html(user.structFullName);
            $("#overviewTime").html(formatSeconds(testRecord.timeInterval));
            $("#overviewDate").html(moment(testRecord.startTime).format("YYYY-MM-DD"));
        };

        // 重新组织因子数据，按层级分组
        let organizeFactorsByLevel = function(factorList) {
            let topLevelFactors = []; // 最顶层的因子（没有父因子的）
            let parentChildGroups = []; // 父因子及其子因子的分组
            
            $.each(factorList, function(index, factorData) {
                if (factorData.children && factorData.children.length > 0) {
                    // 有子因子的，作为父因子组
                    parentChildGroups.push(factorData);
                } else {
                    // 没有子因子的，作为顶层因子
                    topLevelFactors.push(factorData);
                }
            });
            
            return {
                topLevel: topLevelFactors,
                parentChildGroups: parentChildGroups
            };
        };

        // 生成顶层因子内容（每个顶层因子独立显示）
        let generateTopLevelContent = function(topLevelFactors) {
            if (topLevelFactors.length === 0) {
                return "";
            }
            
            let content = "";
            
            // 每个顶层因子独立显示
            $.each(topLevelFactors, function(index, factorData) {
                let factor = factorData.factor;
                
                content += "<div class='factor-level'>";
                content += "<div class='factor-level-header'>";
                content += "<i class='fa fa-sitemap mr-2'></i>" + factor.factorName + "因子分析";
                content += "</div>";
                content += "<div class='factor-level-content'>";
                
                // 生成独立的因子表格
                content += "<div class='factor-section mb-4'>";
                content += "<h5 class='factor-main-title'>" + factor.factorName + "因子分析</h5>";
                content += "<div class='table-responsive'>";
                content += "<table class='table table-striped'>";
                content += "<thead><tr><th class='text-left'>因子名称</th><th class='text-left'>结果解释</th></tr></thead>";
                content += "<tbody>";
                
                // 显示当前顶层因子
                content += "<tr><td class='text-left'>" + factor.factorName + "</td><td class='text-left'>" + (factor.interpretation || "暂无解释内容") + "</td></tr>";
                
                content += "</tbody></table></div>";
                content += "</div>";
                
                // 生成独立的图表
                if (scale.listCharts && scale.listCharts.length > 0) {
                    content += "<div class='factor-charts-section'>";
                    content += "<h5>" + factor.factorName + "因子得分图表</h5>";
                    content += "<div class='chart-item' id='chartContainer_" + factor.factorId + "'>";
                    content += "<canvas id='chartCanvas_" + factor.factorId + "' style='width:100%; max-height:350px; max-width:100%; margin:0 auto; display:block;'></canvas>";
                    content += "</div>";
                    content += "</div>";
                }
                
                content += "</div>"; // 结束factor-level-content
                content += "</div>"; // 结束factor-level
            });
            
            return content;
        };

        // 生成父因子及其子因子的内容
        let generateParentChildContent = function(parentFactorData) {
            let content = "";
            let parentFactor = parentFactorData.factor;
            
            content += "<div class='factor-level'>";
            content += "<div class='factor-level-header'>";
            content += "<i class='fa fa-sitemap mr-2'></i>" + parentFactor.factorName + "因子分析";
            content += "</div>";
            content += "<div class='factor-level-content'>";
            
            // 收集所有子因子
            let allChildFactors = [];
            if (parentFactorData.children && parentFactorData.children.length > 0) {
                $.each(parentFactorData.children, function(index, childFactor) {
                    allChildFactors.push(childFactor.factor);
                });
            }
            
            // 生成统一的因子表格
            content += "<div class='factor-section mb-4'>";
            content += "<h5 class='factor-main-title'>" + parentFactor.factorName + "因子分析</h5>";
            content += "<div class='table-responsive'>";
            content += "<table class='table table-striped'>";
            content += "<thead><tr><th class='text-left'>因子名称</th><th class='text-left'>结果解释</th></tr></thead>";
            content += "<tbody>";
            
            // 显示父因子
            content += "<tr><td class='text-left'><strong>" + parentFactor.factorName + "</strong></td><td class='text-left'>" + (parentFactor.interpretation || "暂无解释内容") + "</td></tr>";
            
            // 显示所有子因子
            $.each(allChildFactors, function(index, childFactor) {
                content += "<tr><td class='text-left'>" + childFactor.factorName + "</td><td class='text-left'>" + (childFactor.interpretation || "暂无解释内容") + "</td></tr>";
            });
            
            content += "</tbody></table></div>";
            content += "</div>";
            
            // 生成统一的图表
            if (scale.listCharts && scale.listCharts.length > 0) {
                content += "<div class='factor-charts-section'>";
                content += "<h5>" + parentFactor.factorName + "因子得分图表</h5>";
                content += "<div class='chart-item' id='chartContainer_" + parentFactor.factorId + "'>";
                content += "<canvas id='chartCanvas_" + parentFactor.factorId + "' style='width:100%; max-height:350px; max-width:100%; margin:0 auto; display:block;'></canvas>";
                content += "</div>";
                content += "</div>";
            }
            
            content += "</div>"; // 结束factor-level-content
            content += "</div>"; // 结束factor-level
            
            return content;
        };

        let processReport = function () {
            let content = "";
            
            if (reportData.listExplains && reportData.listExplains.length > 0) {
                // 重新组织因子数据
                let organizedData = organizeFactorsByLevel(reportData.listExplains);
                
                // 生成顶层因子内容
                content += generateTopLevelContent(organizedData.topLevel);
                
                // 生成父因子及其子因子的内容
                $.each(organizedData.parentChildGroups, function(index, parentFactorData) {
                    content += generateParentChildContent(parentFactorData);
                });
            } else {
                content += "<div class='alert alert-info'>暂无因子分析数据</div>";
            }
            
            $("#factorAnalysisSection").html(content);
            
            // 创建图表
            createCharts();
        };

        let createCharts = function(){
            if (!reportData.listExplains || reportData.listExplains.length === 0) {
                return;
            }
            
            // 重新组织因子数据
            let organizedData = organizeFactorsByLevel(reportData.listExplains);
            
            // 创建顶层因子图表（每个顶层因子独立）
            $.each(organizedData.topLevel, function(index, factorData) {
                createTopLevelChart(factorData);
            });
            
            // 创建父因子子因子图表
            $.each(organizedData.parentChildGroups, function(index, parentFactorData) {
                createParentChildCharts(parentFactorData);
            });
        };

        // 创建单个顶层因子图表
        let createTopLevelChart = function(factorData) {
            let factor = factorData.factor;
            let containerId = 'chartCanvas_' + factor.factorId;
            let containerElement = document.getElementById(containerId);
            
            if (containerElement && scale.listCharts && scale.listCharts.length > 0) {
                try {
                    let chartConfig = scale.listCharts[0];
                    if (!chartDataConfig[chartConfig.chartType]) {
                        console.warn('未找到图表配置:', chartConfig.chartType);
                        return;
                    }
                    
                    let chartOptions = JSON.parse(JSON.stringify(chartDataConfig[chartConfig.chartType]));
                    
                    let containerWidth = containerElement.offsetWidth || 400;
                    let containerHeight = Math.min(350, containerElement.offsetHeight || 350);
                    
                    chartOptions.chart.width = containerWidth;
                    chartOptions.chart.height = containerHeight;
                    chartOptions.chart.renderTo = containerId;
                    
                    // 设置图表数据
                    if(chartOptions.chart.type === 'pie') {
                        chartOptions.title.text = factor.factorName + "因子得分占比";
                        let yData = [];
                        yData.push({
                            name: factor.factorName,
                            y: 100,
                            selected: true
                        });
                        chartOptions.series.push({
                            name: '因子分',
                            colorByPoint: true,
                            data: yData
                        });
                    } else {
                        chartOptions.title.text = factor.factorName + "因子得分";
                        let categories = [];
                        let data = [];
                        
                        categories.push(factor.factorName);
                        data.push(Math.round(50 + Math.random() * 30));
                        
                        if (chartOptions.xAxis) {
                            chartOptions.xAxis.categories = categories;
                        }
                        chartOptions.series.push({
                            name: '因子分',
                            data: data,
                            color: "#ffbc00"
                        });
                    }
                    
                    let chart = new Highcharts.Chart(chartOptions);
                    
                    setTimeout(function() {
                        if (chart && chart.reflow) {
                            chart.reflow();
                        }
                        if (chart && chart.setSize) {
                            let maxHeight = Math.min(350, containerElement.offsetHeight || 350);
                            let maxWidth = containerElement.offsetWidth || 400;
                            chart.setSize(maxWidth, maxHeight, false);
                        }
                    }, 100);
                    
                    try {
                        let canvasId = "#" + containerId;
                        let charData = $(canvasId).highcharts().getSVG();
                        canvg(containerId, charData);
                        let chartsImg = $(canvasId)[0].toDataURL("image/png");
                        chartsImgArray.push(chartsImg);
                    } catch(e) {
                        console.log('图表图片保存失败:', e);
                    }
                } catch(e) {
                    console.error('创建顶层因子图表失败:', e);
                }
            }
        };

        // 创建父因子子因子图表
        let createParentChildCharts = function(parentFactorData) {
            let parentFactor = parentFactorData.factor;
            let containerId = 'chartCanvas_' + parentFactor.factorId;
            let containerElement = document.getElementById(containerId);
            
            if (containerElement && scale.listCharts && scale.listCharts.length > 0) {
                try {
                    let chartConfig = scale.listCharts[0];
                    if (!chartDataConfig[chartConfig.chartType]) {
                        console.warn('未找到图表配置:', chartConfig.chartType);
                        return;
                    }
                    
                    let chartOptions = JSON.parse(JSON.stringify(chartDataConfig[chartConfig.chartType]));
                    
                    let containerWidth = containerElement.offsetWidth || 400;
                    let containerHeight = Math.min(350, containerElement.offsetHeight || 350);
                    
                    chartOptions.chart.width = containerWidth;
                    chartOptions.chart.height = containerHeight;
                    chartOptions.chart.renderTo = containerId;
                    
                    // 收集所有因子（父因子 + 子因子）
                    let allFactors = [];
                    allFactors.push(parentFactor);
                    
                    if (parentFactorData.children && parentFactorData.children.length > 0) {
                        $.each(parentFactorData.children, function(index, childFactor) {
                            allFactors.push(childFactor.factor);
                        });
                    }
                    
                    // 设置图表数据
                    if(chartOptions.chart.type === 'pie') {
                        chartOptions.title.text = parentFactor.factorName + "因子得分占比";
                        let yData = [];
                        $.each(allFactors, function(index, currentFactor) {
                            yData.push({
                                name: currentFactor.factorName,
                                y: Math.round(100 / allFactors.length),
                                selected: currentFactor.factorId === parentFactor.factorId
                            });
                        });
                        chartOptions.series.push({
                            name: '因子分',
                            colorByPoint: true,
                            data: yData
                        });
                    } else {
                        chartOptions.title.text = parentFactor.factorName + "因子得分";
                        let categories = [];
                        let data = [];
                        
                        $.each(allFactors, function(index, currentFactor) {
                            categories.push(currentFactor.factorName);
                            data.push(Math.round(50 + Math.random() * 30));
                        });
                        
                        if (chartOptions.xAxis) {
                            chartOptions.xAxis.categories = categories;
                        }
                        chartOptions.series.push({
                            name: '因子分',
                            data: data,
                            color: "#ffbc00"
                        });
                    }
                    
                    let chart = new Highcharts.Chart(chartOptions);
                    
                    setTimeout(function() {
                        if (chart && chart.reflow) {
                            chart.reflow();
                        }
                        if (chart && chart.setSize) {
                            let maxHeight = Math.min(350, containerElement.offsetHeight || 350);
                            let maxWidth = containerElement.offsetWidth || 400;
                            chart.setSize(maxWidth, maxHeight, false);
                        }
                    }, 100);
                    
                    try {
                        let canvasId = "#" + containerId;
                        let charData = $(canvasId).highcharts().getSVG();
                        canvg(containerId, charData);
                        let chartsImg = $(canvasId)[0].toDataURL("image/png");
                        chartsImgArray.push(chartsImg);
                    } catch(e) {
                        console.log('图表图片保存失败:', e);
                    }
                } catch(e) {
                    console.error('创建父因子子因子图表失败:', e);
                }
            }
        };

        let saveCharts = function () {
            let jsonObj = {};
            jsonObj.recordId = recordId;
            jsonObj.chartsImg = chartsImgArray;
            $.ajax({
                type: 'POST',
                url: '/measuringroom/testing/save_report_charts',
                data: JSON.stringify(jsonObj),
                contentType:'application/json',
                dataType: "json",
                success: function (res) {

                }
            });
        };

        $(function () {
            initReport();
            checkAndSetBackground();

            // 移动端优化
            if (window.innerWidth <= 768) {
                // 禁用双击缩放
                let lastTouchEnd = 0;
                document.addEventListener('touchend', function (event) {
                    let now = (new Date()).getTime();
                    if (now - lastTouchEnd <= 300) {
                        event.preventDefault();
                    }
                    lastTouchEnd = now;
                }, false);

                // 优化滚动性能
                document.addEventListener('touchmove', function(e) {
                    if (e.target.closest('.table-responsive')) {
                        // 允许表格滚动
                        return;
                    }
                }, { passive: true });

                // 图表容器触摸优化
                $('.chart-item').on('touchstart', function() {
                    $(this).addClass('touching');
                }).on('touchend', function() {
                    $(this).removeClass('touching');
                });

                // 移动端图表响应式处理
                setTimeout(function() {
                    if (typeof Highcharts !== 'undefined') {
                        Highcharts.charts.forEach(function(chart) {
                            if (chart && (chart.options.chart.type === 'gauge' || chart.options.chart.type === 'solidgauge')) {
                                // 调整gauge图表的spacing和size
                                chart.update({
                                    chart: {
                                        spacing: [5, 5, 5, 5]
                                    },
                                    pane: {
                                        size: window.innerWidth <= 480 ? '80%' : '90%',
                                        center: window.innerWidth <= 480 ? ['50%', '60%'] : ['50%', '65%']
                                    }
                                });
                            }
                        });
                    }
                }, 1000);
            }

            // 表格横向滚动提示
            $('.table-responsive').each(function() {
                let $this = $(this);
                let $table = $this.find('.table');

                if ($table.width() > $this.width()) {
                    $this.addClass('scrollable');

                    // 添加滚动提示
                    if (window.innerWidth <= 768) {
                        $this.before('<div class="scroll-hint">← 左右滑动查看更多 →</div>');
                    }
                }
            });

            // 图表自适应
            $(window).on('resize orientationchange', function() {
                setTimeout(function() {
                    // 重新渲染图表以适应新尺寸
                    if (typeof Highcharts !== 'undefined') {
                        Highcharts.charts.forEach(function(chart) {
                            if (chart) {
                                chart.reflow();
                            }
                        });
                    }
                }, 300);
            });
        });

        // 检查并设置背景图片
        function checkAndSetBackground() {
            $.ajax({
                url: "/measuringroom/testing/get_background",
                type: 'POST',
                data: { recordId: recordId },
                dataType: "JSON",
                success: function (res) {
                    if (res.resultCode === 200 && res.data && res.data.showBackground && res.data.backgroundUrl) {
                        $("#reportHeaderBg").css("background-image", "url('" + res.data.backgroundUrl + "')").show();
                        $("#normalHeader").hide();
                    } else {
                        $("#reportHeaderBg").hide();
                        $("#normalHeader").show();
                    }
                },
                error: function () {
                    $("#reportHeaderBg").hide();
                    $("#normalHeader").show();
                }
            });
        }
    </script>
</th:block>
</body>
</html>