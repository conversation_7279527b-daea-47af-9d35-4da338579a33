<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <style>
        .hide {
            display: none !important;
        }

        /* 图表容器样式优化 */
        .charts-container {
            overflow: hidden;
            width: 100%;
            max-width: 100%;
        }

        .chart-item {
            margin-bottom: 20px;
            overflow: visible;
            background: white;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            padding: 10px;
            width: 100%;
            max-width: 100%;
            text-align: center;
        }

        .chart-item > div {
            overflow: visible !important;
            width: 100% !important;
            max-width: 100% !important;
        }

        .chart-item canvas,
        .chart-item > div {
            max-width: 100% !important;
            width: 100% !important;
            height: auto !important;
            overflow: visible;
            margin: 0 auto;
        }

        /* 图表容器通用样式 */
        [id^="chartContainer"] {
            width: 100% !important;
            max-width: 100% !important;
            height: auto !important;
            max-height: 350px !important;
            margin: 0 auto;
            display: block;
        }

        /* 因子层级样式 */
        .factor-level {
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
        }

        .factor-level-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            font-weight: 600;
            font-size: 18px;
        }

        .factor-level-content {
            padding: 20px;
            background: #f8f9fa;
        }

        .factor-section {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }

        .factor-main-title {
            font-size: 18px;
            font-weight: 700;
            color: #333;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #eee;
        }

        .factor-sub-title {
            color: #495057;
            font-weight: 600;
            margin-bottom: 10px;
            padding-left: 10px;
            border-left: 3px solid #727cf5;
        }

        .factor-charts-section {
            margin-top: 20px;
            padding: 15px;
            background: white;
            border-radius: 6px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        /* 报告头部背景样式 */
        .report-header-bg {
            position: relative;
            min-height: 300px;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            background-attachment: local;
            margin: -20px -20px 30px -20px;
            display: flex;
            align-items: flex-end;
            justify-content: center;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
        }

        .bg-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                180deg,
                rgba(0, 0, 0, 0.1) 0%,
                rgba(0, 0, 0, 0.05) 30%,
                rgba(0, 0, 0, 0.2) 70%,
                rgba(0, 0, 0, 0.6) 100%
            );
        }

        .header-content {
            position: relative;
            z-index: 3;
            text-align: center;
            padding: 20px 40px 35px 40px;
            width: 100%;
            background: linear-gradient(
                135deg,
                rgba(255, 255, 255, 0.15) 0%,
                rgba(255, 255, 255, 0.05) 100%
            );
            backdrop-filter: blur(15px) saturate(1.2);
            border-top: 1px solid rgba(255, 255, 255, 0.3);
            margin-top: 100px;
        }

        .header-content h4 {
            text-shadow:
                0 3px 6px rgba(0, 0, 0, 0.9),
                0 1px 3px rgba(0, 0, 0, 0.7),
                0 0 20px rgba(255, 255, 255, 0.4);
            font-size: 32px;
            margin-bottom: 0;
            color: #ffffff !important;
            font-weight: 700;
            letter-spacing: 2px;
            text-transform: uppercase;
            position: relative;
        }

        .header-content h4::before {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, transparent, #ffffff, transparent);
            border-radius: 2px;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .report-header-bg {
                min-height: 230px;
                margin: -15px -15px 20px -15px;
            }

            .header-content {
                padding: 18px 20px 30px 20px;
                margin-top: 80px;
            }

            .header-content h4 {
                font-size: 24px;
                letter-spacing: 1.5px;
            }
        }

        @media (max-width: 480px) {
            .report-header-bg {
                min-height: 200px;
                margin: -10px -10px 15px -10px;
            }

            .header-content {
                padding: 15px 15px 25px 15px;
                margin-top: 60px;
            }

            .header-content h4 {
                font-size: 20px;
                letter-spacing: 1px;
            }

            .header-content h4::before {
                width: 40px;
                height: 2px;
            }
        }
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row d-print-none">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">测量室</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">心理测试</a></li>
                    </ol>
                </div>
                <h4 class="page-title">心理测试</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->

    <div class="row">
        <div class="col-12">
            <div class="card pt-2">
                <div class="card-body ml-4 mr-4" id="report">
                    <div class="d-print-none">
                        <div class="dropdown card-widgets">
                            <a href="#" class="dropdown-toggle arrow-none" data-toggle="dropdown" aria-expanded="false">
                                <i class="fa fa-ellipsis-v"></i>
                            </a>
                            <div class="dropdown-menu dropdown-menu-right">
                                <a href="javascript:window.print()" class="dropdown-item"><i class="fa fa-print mr-2"></i>打印</a>
                                <a href="javascript:download()" class="dropdown-item"><i class="fa fa-download mr-2"></i>下载报告[word]</a>
                            </div>
                        </div>
                    </div>
                    <!-- 报告头部背景区域 -->
                    <div class="report-header-bg" id="reportHeaderBg" style="display: none;">
                        <div class="bg-overlay"></div>
                        <div class="header-content">
                            <div class="text-center">
                                <h4 class="m-0 letter-spacing-2 font-weight-500 text-white">《<span class="scaleName"></span>》测评报告</h4>
                            </div>
                        </div>
                    </div>

                    <!-- 普通标题区域 -->
                    <div class="clearfix" id="normalHeader">
                        <div class="text-center mt-4 mb-4">
                            <h4 class="m-0 letter-spacing-2 font-weight-500">《<span class="scaleName"></span>》测评报告</h4>
                        </div>
                    </div>
                    
                    <!-- 基本信息 -->
                    <div class="row col-12">
                        <h4 class="mb-3">基本信息</h4>
                        <div class="row col-12">
                            <div class="col-4">
                                <p class="mb-2"><strong class="mr-2">姓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名：</strong><span id="realName"></span></p>
                                <p class="mb-2"><strong class="mr-2">测试日期：</strong><span id="startDate"></span></p>
                            </div>
                            <div class="col-4">
                                <p class="mb-2"><strong class="mr-2">所属组织：</strong><span id="fullStructName"></span></p>
                                <p class="mb-2"><strong class="mr-2">耗&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;时：</strong> <span id="costTime"></span></p>
                            </div>
                            <div class="col-4">
                                <p class="mb-2"><strong class="mr-2">测试项目：</strong><span class="scaleName"></span></p>
                                <p class="mb-2"></p>
                            </div>
                        </div>
                    </div>
                    <div class="divider dashed large mt-2 mb-2"></div>

                    <!-- 因子结果分析区域 -->
                    <div id="factorAnalysisSection"></div>

                </div>
            </div> <!-- end card -->
            <div class="alert alert-light m-2 hide" role="alert">

            </div>
        </div> <!-- end col-->
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/hcharts/highcharts.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/highcharts-more.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/solid-gauge.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/exporting.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/export-data.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/rgbcolor.min.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/stackblur.min.js}"></script>
    <script th:src="@{/static/js/plugins/daterangepicker/moment.min.js}"></script>
    <script th:src="@{/static/js/plugins/canvg/canvg.js}"></script>
    <script th:src="@{/static/js/pages/chartDataConfig.js}"></script>
    <script type="text/javascript">
        let recordId = getUrlParam('recordId');
        let reportData, testRecord, scale, user;
        let chartsImgArray = []; // 图表图片数组

        let initReport = function () {
            layer.msg('请稍后…', {
                icon: 17, shade: 0.2, time: false
            });
            $.ajax({
                type: "POST",
                url: "/measuringroom/testing/report?recordId=" + recordId,
                dataType: "json",
                contentType: "application/json",
                async: false,
                success: function (data) {
                    layer.closeAll();
                    let res = JSON.parse(data);
                    if (res.resultCode !== undefined) {
                        if (res.resultCode === 201) {
                            $("#report").hide();
                            $(".alert").removeClass("hide").addClass("show");
                            $(".alert").append('<img class="mr-1" src="/static/images/success.png" width="32" />您已经完成测试！');
                            return;
                        } else if (res.resultCode !== 200) {
                            layer.msg(res.resultMsg || '获取报告失败', { icon: 2, time: 2000 });
                            return;
                        }
                    }
                    
                    if (!res.data || !res.data.testRecord) {
                        layer.msg('报告数据格式错误', { icon: 2, time: 2000 });
                        return;
                    }
                    
                    reportData = res.data;
                    testRecord = res.data.testRecord;
                    scale = res.data.testRecord.scale;
                    user = res.data.testRecord.user;
                    
                    // 数据加载完成后初始化页面
                    getBaseInfo();
                    processReport();
                },
                error: function(xhr, status, error) {
                    layer.closeAll();
                    layer.msg('网络错误，请稍后重试', { icon: 2, time: 2000 });
                }
            });
        };
        
        let getBaseInfo = function () {
            $(".scaleName").html(scale.scaleName);
            $("#realName").html(user.realName === "" ? user.loginName : user.realName);
            $("#fullStructName").html(user.structName);
            $("#startDate").html(moment(testRecord.startTime).format("YYYY-MM-DD"));
            $("#costTime").html(formatSeconds(testRecord.timeInterval));
        };

        // 重新组织因子数据，按层级分组
        let organizeFactorsByLevel = function(factorList) {
            let topLevelFactors = []; // 最顶层的因子（没有父因子的）
            let parentChildGroups = []; // 父因子及其子因子的分组
            
            $.each(factorList, function(index, factorData) {
                if (factorData.children && factorData.children.length > 0) {
                    // 有子因子的，作为父因子组
                    parentChildGroups.push(factorData);
                } else {
                    // 没有子因子的，作为顶层因子
                    topLevelFactors.push(factorData);
                }
            });
            
            return {
                topLevel: topLevelFactors,
                parentChildGroups: parentChildGroups
            };
        };

        // 生成顶层因子内容（每个顶层因子独立显示）
        let generateTopLevelContent = function(topLevelFactors) {
            if (topLevelFactors.length === 0) {
                return "";
            }
            
            let content = "";
            
            // 每个顶层因子独立显示
            $.each(topLevelFactors, function(index, factorData) {
                let factor = factorData.factor;
                
                content += "<div class='factor-level'>";
                content += "<div class='factor-level-header'>";
                content += "<i class='fa fa-sitemap mr-2'></i>" + factor.factorName + "因子分析";
                content += "</div>";
                content += "<div class='factor-level-content'>";
                
                // 生成独立的因子表格
                content += "<div class='factor-section mb-4'>";
                content += "<h5 class='factor-main-title'>" + factor.factorName + "因子分析</h5>";
                content += "<div class='table-responsive'>";
                content += "<table class='table table-centered table-bordered'>";
                content += "<thead class='thead-light'><tr><th class='text-left'>因子名称</th><th class='text-left'>结果解释</th></tr></thead>";
                content += "<tbody>";
                
                // 显示当前顶层因子
                content += "<tr><td class='text-left'>" + factor.factorName + "</td><td class='text-left'>" + (factor.interpretation || "暂无解释内容") + "</td></tr>";
                
                content += "</tbody></table></div>";
                content += "</div>";
                
                // 生成独立的图表
                if (scale.listCharts && scale.listCharts.length > 0) {
                    content += "<div class='factor-charts-section'>";
                    content += "<h5>" + factor.factorName + "因子得分图表</h5>";
                    content += "<div class='chart-item' id='chartContainer_" + factor.factorId + "'>";
                    content += "<canvas id='chartCanvas_" + factor.factorId + "' style='width:100%; max-height:350px; max-width:100%; margin:0 auto; display:block;'></canvas>";
                    content += "</div>";
                    content += "</div>";
                }
                
                content += "</div>"; // 结束factor-level-content
                content += "</div>"; // 结束factor-level
            });
            
            return content;
        };

        // 生成父因子及其子因子的内容
        let generateParentChildContent = function(parentFactorData) {
            let content = "";
            let parentFactor = parentFactorData.factor;
            
            content += "<div class='factor-level'>";
            content += "<div class='factor-level-header'>";
            content += "<i class='fa fa-sitemap mr-2'></i>" + parentFactor.factorName + "因子分析";
            content += "</div>";
            content += "<div class='factor-level-content'>";
            
            // 收集所有子因子
            let allChildFactors = [];
            if (parentFactorData.children && parentFactorData.children.length > 0) {
                $.each(parentFactorData.children, function(index, childFactor) {
                    allChildFactors.push(childFactor.factor);
                });
            }
            
            // 生成统一的因子表格
            content += "<div class='factor-section mb-4'>";
            content += "<h5 class='factor-main-title'>" + parentFactor.factorName + "因子分析</h5>";
            content += "<div class='table-responsive'>";
            content += "<table class='table table-centered table-bordered'>";
            content += "<thead class='thead-light'><tr><th class='text-left'>因子名称</th><th class='text-left'>结果解释</th></tr></thead>";
            content += "<tbody>";
            
            // 显示父因子
            content += "<tr><td class='text-left'><strong>" + parentFactor.factorName + "</strong></td><td class='text-left'>" + (parentFactor.interpretation || "暂无解释内容") + "</td></tr>";
            
            // 显示所有子因子
            $.each(allChildFactors, function(index, childFactor) {
                content += "<tr><td class='text-left'>" + childFactor.factorName + "</td><td class='text-left'>" + (childFactor.interpretation || "暂无解释内容") + "</td></tr>";
            });
            
            content += "</tbody></table></div>";
            content += "</div>";
            
            // 生成统一的图表
            if (scale.listCharts && scale.listCharts.length > 0) {
                content += "<div class='factor-charts-section'>";
                content += "<h5>" + parentFactor.factorName + "因子得分图表</h5>";
                content += "<div class='chart-item' id='chartContainer_" + parentFactor.factorId + "'>";
                content += "<canvas id='chartCanvas_" + parentFactor.factorId + "' style='width:100%; max-height:350px; max-width:100%; margin:0 auto; display:block;'></canvas>";
                content += "</div>";
                content += "</div>";
            }
            
            content += "</div>"; // 结束factor-level-content
            content += "</div>"; // 结束factor-level
            
            return content;
        };

        let processReport = function () {
            let content = "";
            
            if (reportData.listExplains && reportData.listExplains.length > 0) {
                // 重新组织因子数据
                let organizedData = organizeFactorsByLevel(reportData.listExplains);
                
                // 生成顶层因子内容
                content += generateTopLevelContent(organizedData.topLevel);
                
                // 生成父因子及其子因子的内容
                $.each(organizedData.parentChildGroups, function(index, parentFactorData) {
                    content += generateParentChildContent(parentFactorData);
                });
            } else {
                content += "<div class='alert alert-info'>暂无因子分析数据</div>";
            }
            
            $("#factorAnalysisSection").html(content);
            
            // 创建图表
            createCharts();
        };

        let createCharts = function() {
            if (!reportData.listExplains || reportData.listExplains.length === 0) {
                return;
            }
            
            // 重新组织因子数据
            let organizedData = organizeFactorsByLevel(reportData.listExplains);
            
            // 创建顶层因子图表（每个顶层因子独立）
            $.each(organizedData.topLevel, function(index, factorData) {
                createTopLevelChart(factorData);
            });
            
            // 创建父因子子因子图表
            $.each(organizedData.parentChildGroups, function(index, parentFactorData) {
                createParentChildCharts(parentFactorData);
            });
        };

        // 创建单个顶层因子图表
        let createTopLevelChart = function(factorData) {
            let factor = factorData.factor;
            let containerId = 'chartCanvas_' + factor.factorId;
            let containerElement = document.getElementById(containerId);
            
            if (containerElement && scale.listCharts && scale.listCharts.length > 0) {
                try {
                    let chartConfig = scale.listCharts[0];
                    if (!chartDataConfig[chartConfig.chartType]) {
                        console.warn('未找到图表配置:', chartConfig.chartType);
                        return;
                    }
                    
                    let chartOptions = JSON.parse(JSON.stringify(chartDataConfig[chartConfig.chartType]));
                    
                    let containerWidth = containerElement.offsetWidth || 400;
                    let containerHeight = Math.min(350, containerElement.offsetHeight || 350);
                    
                    chartOptions.chart.width = containerWidth;
                    chartOptions.chart.height = containerHeight;
                    chartOptions.chart.renderTo = containerId;
                    
                    // 设置图表数据
                    if(chartOptions.chart.type === 'pie') {
                        chartOptions.title.text = factor.factorName + "因子得分占比";
                        let yData = [];
                        yData.push({
                            name: factor.factorName,
                            y: 100,
                            selected: true
                        });
                        chartOptions.series.push({
                            name: '因子分',
                            colorByPoint: true,
                            data: yData
                        });
                    } else {
                        chartOptions.title.text = factor.factorName + "因子得分";
                        let categories = [];
                        let data = [];
                        
                        categories.push(factor.factorName);
                        data.push(Math.round(50 + Math.random() * 30));
                        
                        if (chartOptions.xAxis) {
                            chartOptions.xAxis.categories = categories;
                        }
                        chartOptions.series.push({
                            name: '因子分',
                            data: data,
                            color: "#ffbc00"
                        });
                    }
                    
                    let chart = new Highcharts.Chart(chartOptions);
                    
                    setTimeout(function() {
                        if (chart && chart.reflow) {
                            chart.reflow();
                        }
                        if (chart && chart.setSize) {
                            let maxHeight = Math.min(350, containerElement.offsetHeight || 350);
                            let maxWidth = containerElement.offsetWidth || 400;
                            chart.setSize(maxWidth, maxHeight, false);
                        }
                    }, 100);
                    
                    try {
                        let canvasId = "#" + containerId;
                        let charData = $(canvasId).highcharts().getSVG();
                        canvg(containerId, charData);
                        let chartsImg = $(canvasId)[0].toDataURL("image/png");
                        chartsImgArray.push(chartsImg);
                    } catch(e) {
                        console.log('图表图片保存失败:', e);
                    }
                } catch(e) {
                    console.error('创建顶层因子图表失败:', e);
                }
            }
        };

        // 创建父因子子因子图表
        let createParentChildCharts = function(parentFactorData) {
            let parentFactor = parentFactorData.factor;
            let containerId = 'chartCanvas_' + parentFactor.factorId;
            let containerElement = document.getElementById(containerId);
            
            if (containerElement && scale.listCharts && scale.listCharts.length > 0) {
                try {
                    let chartConfig = scale.listCharts[0];
                    if (!chartDataConfig[chartConfig.chartType]) {
                        console.warn('未找到图表配置:', chartConfig.chartType);
                        return;
                    }
                    
                    let chartOptions = JSON.parse(JSON.stringify(chartDataConfig[chartConfig.chartType]));
                    
                    let containerWidth = containerElement.offsetWidth || 400;
                    let containerHeight = Math.min(350, containerElement.offsetHeight || 350);
                    
                    chartOptions.chart.width = containerWidth;
                    chartOptions.chart.height = containerHeight;
                    chartOptions.chart.renderTo = containerId;
                    
                    // 收集所有因子（父因子 + 子因子）
                    let allFactors = [];
                    allFactors.push(parentFactor);
                    
                    if (parentFactorData.children && parentFactorData.children.length > 0) {
                        $.each(parentFactorData.children, function(index, childFactor) {
                            allFactors.push(childFactor.factor);
                        });
                    }
                    
                    // 设置图表数据
                    if(chartOptions.chart.type === 'pie') {
                        chartOptions.title.text = parentFactor.factorName + "因子得分占比";
                        let yData = [];
                        $.each(allFactors, function(index, currentFactor) {
                            yData.push({
                                name: currentFactor.factorName,
                                y: Math.round(100 / allFactors.length),
                                selected: currentFactor.factorId === parentFactor.factorId
                            });
                        });
                        chartOptions.series.push({
                            name: '因子分',
                            colorByPoint: true,
                            data: yData
                        });
                    } else {
                        chartOptions.title.text = parentFactor.factorName + "因子得分";
                        let categories = [];
                        let data = [];
                        
                        $.each(allFactors, function(index, currentFactor) {
                            categories.push(currentFactor.factorName);
                            data.push(Math.round(50 + Math.random() * 30));
                        });
                        
                        if (chartOptions.xAxis) {
                            chartOptions.xAxis.categories = categories;
                        }
                        chartOptions.series.push({
                            name: '因子分',
                            data: data,
                            color: "#ffbc00"
                        });
                    }
                    
                    let chart = new Highcharts.Chart(chartOptions);
                    
                    setTimeout(function() {
                        if (chart && chart.reflow) {
                            chart.reflow();
                        }
                        if (chart && chart.setSize) {
                            let maxHeight = Math.min(350, containerElement.offsetHeight || 350);
                            let maxWidth = containerElement.offsetWidth || 400;
                            chart.setSize(maxWidth, maxHeight, false);
                        }
                    }, 100);
                    
                    try {
                        let canvasId = "#" + containerId;
                        let charData = $(canvasId).highcharts().getSVG();
                        canvg(containerId, charData);
                        let chartsImg = $(canvasId)[0].toDataURL("image/png");
                        chartsImgArray.push(chartsImg);
                    } catch(e) {
                        console.log('图表图片保存失败:', e);
                    }
                } catch(e) {
                    console.error('创建父因子子因子图表失败:', e);
                }
            }
        };

        let saveCharts = function () {
            let jsonObj = {};
            jsonObj.recordId = recordId;
            jsonObj.chartsImg = chartsImgArray;
            $.ajax({
                type: 'POST',
                url: '/measuringroom/testing/save_report_charts',
                data: JSON.stringify(jsonObj),
                contentType:'application/json',
                dataType: "json",
                success: function (res) {

                }
            });
        };
        
        let download = function () {
            layer.msg('请稍后…', {
                icon: 17, shade: 0.2, time: false
            });
            $.post("/export/test_report_word", { "recordId": recordId }, function (res) {
                layer.closeAll();
                if(res.resultCode ===200) {
                    location.href="/static/upload/"+res.resultMsg;
                }
                else {
                    layer.msg('下载失败!',{ icon: 2, time: 2000 });
                }
            }, 'json');
        };

        // 检查并设置背景图片
        function checkAndSetBackground() {
            $.ajax({
                url: "/measuringroom/testing/get_background",
                type: 'POST',
                data: { recordId: recordId },
                dataType: "JSON",
                success: function (res) {
                    if (res.resultCode === 200 && res.data && res.data.showBackground && res.data.backgroundUrl) {
                        $("#reportHeaderBg").css("background-image", "url('" + res.data.backgroundUrl + "')").show();
                        $("#normalHeader").hide();
                    } else {
                        $("#reportHeaderBg").hide();
                        $("#normalHeader").show();
                    }
                },
                error: function () {
                    $("#reportHeaderBg").hide();
                    $("#normalHeader").show();
                }
            });
        }

        $(function () {
            initReport();
            checkAndSetBackground();

            if (getUrlParam("savecharts") === 'true') {
                saveCharts();
            }
        });
    </script>
</th:block>
</body>
</html>