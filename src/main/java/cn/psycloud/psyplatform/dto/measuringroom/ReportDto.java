package cn.psycloud.psyplatform.dto.measuringroom;

import cn.psycloud.psyplatform.entity.measuringroom.TestRecordChartsEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import lombok.Data;

import java.util.List;

/**
 *  测评报告实体类
 */
@Data
public class ReportDto {
    private static final long serialVersionUID = 1L;
    private TestRecordDto testRecord;
    private List<FactorExplainHierarchyDto> listExplains;
    private List<TestRecordChartsEntity> listCharts;

}
