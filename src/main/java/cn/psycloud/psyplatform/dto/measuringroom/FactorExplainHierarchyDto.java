package cn.psycloud.psyplatform.dto.measuringroom;

import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import lombok.Data;
import java.util.ArrayList;
import java.util.List;

@Data
public class FactorExplainHierarchyDto {
    private TestRecordExplainEntity factor;
    private List<FactorExplainHierarchyDto> children;
    
    public FactorExplainHierarchyDto() {
        this.children = new ArrayList<>();
    }
    
    public boolean hasChildren() {
        return children != null && !children.isEmpty();
    }
}